/**
 * 广告追踪API服务
 * 封装所有广告追踪相关的API调用
 */

const AdTrackingApi = {
  /**
   * 创建广告跟踪记录（已注册用户）
   * @param {Object} params - 跟踪参数
   * @returns {Promise} API响应
   */
  createAdTracking: (params) => {
    return useClientPost('/member/ad-tracking/create', {
      body: params,
      custom: {
        showError: false, // 静默处理错误，避免影响用户体验
        showSuccess: false,
        showLoading: false
      }
    });
  },

  /**
   * 创建访客广告跟踪记录（未注册用户）
   * @param {Object} params - 跟踪参数
   * @returns {Promise} API响应
   */
  createVisitorAdTracking: (params) => {
    return useClientPost('/member/ad-tracking/create-visitor', {
      body: params,
      custom: {
        showError: false, // 静默处理错误
        showSuccess: false,
        showLoading: false
      }
    });
  },

  /**
   * 关联访客记录到用户
   * @param {string} sessionId - 会话ID
   * @returns {Promise} API响应
   */
  linkVisitorToUser: (sessionId) => {
    return useClientPost('/member/ad-tracking/link-visitor-to-user', {
      params: { sessionId },
      custom: {
        showError: false,
        showSuccess: false,
        showLoading: false
      }
    });
  },

  /**
   * 记录注册转化
   * @param {string} sessionId - 会话ID
   * @returns {Promise} API响应
   */
  recordRegisterConversion: (sessionId) => {
    const params = sessionId ? { sessionId } : {};
    return useClientPost('/member/ad-tracking/record-register-conversion', {
      params,
      custom: {
        showError: false,
        showSuccess: false,
        showLoading: false
      }
    });
  },

  /**
   * 更新转化信息
   * @param {Object} params - 转化参数
   * @returns {Promise} API响应
   */
  updateConversion: (params) => {
    return useClientPut('/member/ad-tracking/update-conversion', {
      body: params,
      custom: {
        showError: false,
        showSuccess: false,
        showLoading: false
      }
    });
  },

  /**
   * 获取用户广告跟踪记录列表
   * @returns {Promise} API响应
   */
  getUserAdTrackingList: () => {
    return useClientGet('/member/ad-tracking/list-by-user', {
      custom: {
        showError: true,
        showLoading: false,
        auth: true
      }
    });
  },

  /**
   * 根据会话ID获取广告跟踪记录
   * @param {string} sessionId - 会话ID
   * @returns {Promise} API响应
   */
  getAdTrackingBySession: (sessionId) => {
    return useClientGet('/member/ad-tracking/get-by-session', {
      params: { sessionId },
      custom: {
        showError: false,
        showLoading: false
      }
    });
  },

  /**
   * 分页查询广告跟踪记录（管理端使用）
   * @param {Object} params - 查询参数
   * @returns {Promise} API响应
   */
  getAdTrackingPage: (params) => {
    return useClientGet('/member/ad-tracking/page', {
      params,
      custom: {
        showError: true,
        showLoading: true,
        auth: true
      }
    });
  },

  /**
   * 获取访客广告跟踪记录列表（管理端使用）
   * @returns {Promise} API响应
   */
  getVisitorAdTrackingList: () => {
    return useClientGet('/member/ad-tracking/visitor-list', {
      custom: {
        showError: true,
        showLoading: true,
        auth: true
      }
    });
  }
};

export default AdTrackingApi;
