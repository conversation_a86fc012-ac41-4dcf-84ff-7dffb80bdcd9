/**
 * 广告追踪组合式函数
 * 提供页面访问处理、用户登录处理、转化记录等高级功能
 */

import { AdTrackingUtil } from '~/utils/adTracking';
import AdTrackingApi from '~/composables/adTrackingApi';
import { useUserStore } from '~/store/user';
import { useAuthStore } from '~/store/auth';

export const useAdTracking = () => {
  const { isLogin } = useAuthStore();
  const userStore = useUserStore();

  /**
   * 处理页面访问的广告跟踪
   * 在页面加载时调用，自动检测和处理广告参数
   */
  const handlePageVisit = async () => {
    try {
      // 提取URL中的广告参数
      const adParams = AdTrackingUtil.extractParamsFromUrl();

      if (Object.keys(adParams).length > 0) {
        // 清理和验证参数
        const sanitizedParams = AdTrackingUtil.sanitizeParams(adParams);

        if (AdTrackingUtil.validateParams(sanitizedParams)) {
          // 保存参数到Cookie
          AdTrackingUtil.saveParams(sanitizedParams);

          // 如果用户已登录，立即创建跟踪记录
          if (isLogin && userStore.userInfo?.id) {
            await createUserTrackingRecord(userStore.userInfo.id, sanitizedParams);
          } else {
            // 创建访客跟踪记录
            await createVisitorTrackingRecord(sanitizedParams);
          }

          console.log('广告跟踪参数已处理:', sanitizedParams);
        }
      }
    } catch (error) {
      console.error('处理页面访问广告跟踪失败:', error);
    }
  };

  /**
   * 创建用户跟踪记录
   * @param {number} userId - 用户ID
   * @param {Object} adParams - 广告参数
   */
  const createUserTrackingRecord = async (userId, adParams) => {
    try {
      const trackingData = AdTrackingUtil.formatForApi(adParams || AdTrackingUtil.getParams());

      if (Object.keys(trackingData).filter((key) => trackingData[key]).length > 1) {
        // 除了sessionId外至少有一个参数
        trackingData.userId = userId;

        const response = await AdTrackingApi.createAdTracking(trackingData);

        if (response?.code === 0) {
          console.log('用户广告跟踪记录创建成功:', response.data);
          return response.data;
        }
      }
    } catch (error) {
      console.error('创建用户广告跟踪记录失败:', error);
    }
    return null;
  };

  /**
   * 创建访客跟踪记录
   * @param {Object} adParams - 广告参数
   */
  const createVisitorTrackingRecord = async (adParams) => {
    try {
      const trackingData = AdTrackingUtil.formatForApi(adParams || AdTrackingUtil.getParams());

      if (Object.keys(trackingData).filter((key) => trackingData[key]).length > 1) {
        // 除了sessionId外至少有一个参数
        const response = await AdTrackingApi.createVisitorAdTracking(trackingData);

        if (response?.code === 0) {
          console.log('访客广告跟踪记录创建成功:', response.data);
          return response.data;
        }
      }
    } catch (error) {
      console.error('创建访客广告跟踪记录失败:', error);
    }
    return null;
  };

  /**
   * 处理用户登录后的广告跟踪
   * 在用户登录成功后调用
   * @param {number} userId - 用户ID
   */
  const handleUserLogin = async (userId) => {
    try {
      const sessionId = AdTrackingUtil.getSessionId();

      if (sessionId) {
        // 尝试关联访客记录到用户
        const linkResponse = await AdTrackingApi.linkVisitorToUser(sessionId);

        if (linkResponse?.code === 0) {
          console.log('访客记录已关联到用户');
        } else {
          // 如果没有访客记录，但有广告参数，创建新的用户记录
          const adParams = AdTrackingUtil.getParams();
          if (AdTrackingUtil.validateParams(adParams)) {
            await createUserTrackingRecord(userId, adParams);
          }
        }
      }
    } catch (error) {
      console.error('处理用户登录广告跟踪失败:', error);
    }
  };

  /**
   * 处理用户注册转化
   * 在用户注册成功后调用
   * @param {number} userId - 用户ID
   */
  const handleRegisterConversion = async (userId) => {
    try {
      // 先处理登录逻辑
      await handleUserLogin(userId);

      // 记录注册转化
      const sessionId = AdTrackingUtil.getSessionId();
      if (sessionId) {
        const response = await AdTrackingApi.recordRegisterConversion(sessionId);

        if (response?.code === 0) {
          console.log('注册转化记录成功');
        }
      }
    } catch (error) {
      console.error('处理注册转化失败:', error);
    }
  };

  /**
   * 处理订单转化
   * 在订单支付成功后调用
   * @param {number} orderId - 订单ID
   * @param {number} orderValue - 订单金额
   */
  const handleOrderConversion = async (orderId, orderValue) => {
    try {
      // 获取当前用户的广告跟踪记录
      const trackingList = await AdTrackingApi.getUserAdTrackingList();

      if (trackingList?.code === 0 && trackingList.data?.length > 0) {
        // 找到最近的未转化记录
        const latestRecord = trackingList.data.filter((record) => !record.isConverted || record.conversionType !== 'ORDER').sort((a, b) => new Date(b.createTime) - new Date(a.createTime))[0];

        if (latestRecord) {
          const response = await AdTrackingApi.updateConversion({
            id: latestRecord.id,
            conversionType: 'ORDER',
            conversionValue: orderValue,
          });

          if (response?.code === 0) {
            console.log('订单转化记录成功');
          }
        }
      }
    } catch (error) {
      console.error('处理订单转化失败:', error);
    }
  };

  /**
   * 获取用户的广告跟踪记录
   * @returns {Promise<Array>} 跟踪记录列表
   */
  const getUserTrackingRecords = async () => {
    try {
      const response = await AdTrackingApi.getUserAdTrackingList();
      return response?.code === 0 ? response.data : [];
    } catch (error) {
      console.error('获取用户广告跟踪记录失败:', error);
      return [];
    }
  };

  /**
   * 清除广告跟踪数据
   */
  const clearTrackingData = () => {
    AdTrackingUtil.clearParams();
    console.log('广告跟踪数据已清除');
  };

  /**
   * 检查是否有活跃的广告跟踪
   * @returns {boolean} 是否有活跃跟踪
   */
  const hasActiveTracking = () => {
    return AdTrackingUtil.hasTrackingParams();
  };

  /**
   * 获取当前跟踪参数
   * @returns {Object} 跟踪参数
   */
  const getCurrentTrackingParams = () => {
    return AdTrackingUtil.getParams();
  };

  return {
    // 核心功能
    handlePageVisit,
    handleUserLogin,
    handleRegisterConversion,
    handleOrderConversion,

    // 记录管理
    createUserTrackingRecord,
    createVisitorTrackingRecord,
    getUserTrackingRecords,

    // 工具方法
    clearTrackingData,
    hasActiveTracking,
    getCurrentTrackingParams,
  };
};
