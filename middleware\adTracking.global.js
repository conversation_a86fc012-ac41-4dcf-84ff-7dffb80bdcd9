/**
 * 广告追踪全局中间件
 * 自动处理URL参数提取和访客记录创建
 */

import { AdTrackingUtil } from '~/utils/adTracking';

export default defineNuxtRouteMiddleware((to, from) => {
  // 服务端渲染时跳过
  if (process.server) return;

  // 检查是否有广告参数
  const hasAdParams = to.query.utm_source || 
                     to.query.utm_medium || 
                     to.query.utm_campaign || 
                     to.query.utm_term || 
                     to.query.utm_content || 
                     to.query.gclid || 
                     to.query.fbclid;

  if (hasAdParams) {
    try {
      // 提取并验证广告参数
      const adParams = AdTrackingUtil.extractParamsFromUrl();
      const sanitizedParams = AdTrackingUtil.sanitizeParams(adParams);
      
      if (AdTrackingUtil.validateParams(sanitizedParams)) {
        // 保存广告参数到Cookie
        AdTrackingUtil.saveParams(sanitizedParams);
        
        console.log('广告参数已保存:', sanitizedParams);
        
        // 可选：清理URL中的广告参数，避免用户看到复杂的URL
        // 注意：这会导致页面重定向，可能影响用户体验
        // 建议根据实际需求决定是否启用
        const shouldCleanUrl = false; // 可以通过配置控制
        
        if (shouldCleanUrl) {
          const cleanQuery = { ...to.query };
          
          // 移除广告参数
          delete cleanQuery.utm_source;
          delete cleanQuery.utm_medium;
          delete cleanQuery.utm_campaign;
          delete cleanQuery.utm_term;
          delete cleanQuery.utm_content;
          delete cleanQuery.gclid;
          delete cleanQuery.fbclid;
          
          // 如果查询参数发生了变化，重定向到清理后的URL
          if (Object.keys(cleanQuery).length !== Object.keys(to.query).length) {
            return navigateTo({
              path: to.path,
              query: cleanQuery
            }, { replace: true });
          }
        }
      }
    } catch (error) {
      console.error('处理广告参数失败:', error);
    }
  }
  
  // 在客户端异步处理广告跟踪记录创建
  // 使用 nextTick 确保页面渲染完成后再执行
  if (process.client) {
    nextTick(async () => {
      try {
        const { handlePageVisit } = useAdTracking();
        await handlePageVisit();
      } catch (error) {
        console.error('处理页面访问广告跟踪失败:', error);
      }
    });
  }
});
