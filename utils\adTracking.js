/**
 * 广告追踪工具类
 * 用于处理UTM参数、Google Ads、Facebook Ads等广告平台的跟踪
 */

/**
 * 广告跟踪参数接口
 */
export const AdTrackingParams = {
  utm_source: '',
  utm_medium: '',
  utm_campaign: '',
  utm_term: '',
  utm_content: '',
  gclid: '',
  fbclid: ''
};

export class AdTrackingUtil {
  static STORAGE_KEY = 'ad_tracking_params';
  static SESSION_KEY = 'ad_tracking_session';

  /**
   * 从URL参数中提取广告跟踪参数
   * @returns {Object} 广告跟踪参数对象
   */
  static extractParamsFromUrl() {
    if (process.server) return {};
    
    const urlParams = new URLSearchParams(window.location.search);
    const params = {};

    // UTM参数
    if (urlParams.get('utm_source')) params.utm_source = urlParams.get('utm_source');
    if (urlParams.get('utm_medium')) params.utm_medium = urlParams.get('utm_medium');
    if (urlParams.get('utm_campaign')) params.utm_campaign = urlParams.get('utm_campaign');
    if (urlParams.get('utm_term')) params.utm_term = urlParams.get('utm_term');
    if (urlParams.get('utm_content')) params.utm_content = urlParams.get('utm_content');

    // 平台特定参数
    if (urlParams.get('gclid')) params.gclid = urlParams.get('gclid');
    if (urlParams.get('fbclid')) params.fbclid = urlParams.get('fbclid');

    return params;
  }

  /**
   * 保存广告跟踪参数到Cookie
   * @param {Object} params - 广告跟踪参数
   */
  static saveParams(params) {
    if (process.server || Object.keys(params).length === 0) return;

    const cookie = useCookie(this.STORAGE_KEY, {
      maxAge: 30 * 24 * 60 * 60, // 30天
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    });
    cookie.value = JSON.stringify(params);
  }

  /**
   * 从Cookie获取广告跟踪参数
   * @returns {Object} 广告跟踪参数对象
   */
  static getParams() {
    if (process.server) return {};

    const cookie = useCookie(this.STORAGE_KEY);
    try {
      return cookie.value ? JSON.parse(cookie.value) : {};
    } catch (error) {
      console.warn('解析广告跟踪参数失败:', error);
      return {};
    }
  }

  /**
   * 生成会话ID
   * @returns {string} 会话ID
   */
  static generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取或创建会话ID
   * @returns {string} 会话ID
   */
  static getSessionId() {
    if (process.server) return '';

    const cookie = useCookie(this.SESSION_KEY, {
      maxAge: 24 * 60 * 60, // 24小时
      sameSite: 'lax',
      secure: process.env.NODE_ENV === 'production'
    });

    if (!cookie.value) {
      cookie.value = this.generateSessionId();
    }

    return cookie.value;
  }

  /**
   * 清除广告跟踪数据
   */
  static clearParams() {
    if (process.server) return;

    const paramsCookie = useCookie(this.STORAGE_KEY);
    const sessionCookie = useCookie(this.SESSION_KEY);
    
    paramsCookie.value = null;
    sessionCookie.value = null;
  }

  /**
   * 检查是否有广告跟踪参数
   * @returns {boolean} 是否有广告跟踪参数
   */
  static hasTrackingParams() {
    const params = this.getParams();
    return Object.keys(params).length > 0;
  }

  /**
   * 获取当前页面信息
   * @returns {Object} 页面信息
   */
  static getPageInfo() {
    if (process.server) return {};

    return {
      landingPage: window.location.href,
      referrer: document.referrer,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 获取客户端IP地址（需要后端支持）
   * @returns {string} IP地址
   */
  static getClientIP() {
    // 这里返回空字符串，实际IP地址由后端获取
    return '';
  }

  /**
   * 验证广告跟踪参数
   * @param {Object} params - 广告跟踪参数
   * @returns {boolean} 参数是否有效
   */
  static validateParams(params) {
    if (!params || typeof params !== 'object') return false;
    
    // 至少需要有一个有效的跟踪参数
    const validKeys = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'gclid', 'fbclid'];
    return validKeys.some(key => params[key] && typeof params[key] === 'string' && params[key].trim().length > 0);
  }

  /**
   * 清理和标准化参数
   * @param {Object} params - 原始参数
   * @returns {Object} 清理后的参数
   */
  static sanitizeParams(params) {
    if (!params || typeof params !== 'object') return {};
    
    const sanitized = {};
    const validKeys = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'gclid', 'fbclid'];
    
    validKeys.forEach(key => {
      if (params[key] && typeof params[key] === 'string') {
        // 移除前后空格，限制长度
        const value = params[key].trim().substring(0, 255);
        if (value.length > 0) {
          sanitized[key] = value;
        }
      }
    });
    
    return sanitized;
  }

  /**
   * 格式化参数用于API调用
   * @param {Object} params - 广告跟踪参数
   * @returns {Object} 格式化后的参数
   */
  static formatForApi(params) {
    const sanitized = this.sanitizeParams(params);
    const pageInfo = this.getPageInfo();
    
    return {
      sessionId: this.getSessionId(),
      utmSource: sanitized.utm_source,
      utmMedium: sanitized.utm_medium,
      utmCampaign: sanitized.utm_campaign,
      utmTerm: sanitized.utm_term,
      utmContent: sanitized.utm_content,
      gclid: sanitized.gclid,
      fbclid: sanitized.fbclid,
      landingPage: pageInfo.landingPage,
      referrer: pageInfo.referrer,
      userAgent: pageInfo.userAgent
    };
  }

  /**
   * 检查URL是否包含广告参数
   * @param {string} url - 要检查的URL
   * @returns {boolean} 是否包含广告参数
   */
  static hasAdParamsInUrl(url) {
    if (!url) return false;
    
    try {
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);
      
      const adParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'gclid', 'fbclid'];
      return adParams.some(param => params.has(param));
    } catch (error) {
      return false;
    }
  }

  /**
   * 从URL中移除广告参数
   * @param {string} url - 原始URL
   * @returns {string} 清理后的URL
   */
  static removeAdParamsFromUrl(url) {
    if (!url) return url;
    
    try {
      const urlObj = new URL(url);
      const params = new URLSearchParams(urlObj.search);
      
      // 移除广告参数
      const adParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content', 'gclid', 'fbclid'];
      adParams.forEach(param => params.delete(param));
      
      urlObj.search = params.toString();
      return urlObj.toString();
    } catch (error) {
      return url;
    }
  }
}
